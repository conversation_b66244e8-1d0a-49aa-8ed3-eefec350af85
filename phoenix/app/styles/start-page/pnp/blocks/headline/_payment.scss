.payment-form {
    $self: &;

    &__amounts {
        display: flex;
        gap: 12px;
        width: 100%;
        margin-bottom: 16px;

        &__button {
            @include get-typography("label-bold");
            border-radius: 100px;
            flex: 1;
            font-family: inherit;
            height: 36px;
            line-height: 15px;
            padding: 0 !important;

            &:hover {
                opacity: 0.9;
                cursor: pointer;
            }
        }
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        /* display: none; <- Crashes Chrome on hover */
        -webkit-appearance: none;
        margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
    }

    input[type="number"] {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }

    &__input {
        @include get-typography("heading-2");
        font-family: inherit;
        height: 52px;
        padding: 0;
        text-align: center;
        width: 100%;
        border: 0;
        outline: 1px solid $sp-input-border;
        border-radius: $border-radius-md;
        background-color: $sp-input-background;
        color: $sp-input-color;
        margin-bottom: 16px;

        &--with-deposit-limits {
            width: 100%;
        }

        &--with-suffix {
            input {
                margin-bottom: 0
            }
        }

        &::placeholder {
            color: $sp-input-placeholder;
            @include get-typography('heading-3');
            text-transform: capitalize;
        }

        &:focus {
            color: $sp-input-border-active;
            outline: 1px solid $sp-input-border-active;
        }

        &--error {
            outline: 1px solid $sp-input-border-error;
        }

        &--rounded {
            border-radius: 57px;
        }

        &--large {
            @include get-typography('heading-1');
            height: 56px;
            color: $color-font-dark;
        }

        &--with-suffix {
            position: relative;
            display: flex;
            align-items: center;
            flex-grow: 0;
            background-color: $sp-input-background;
            border-radius: 100px;
            width: 100%;

            &:focus-within {
                outline: 1px solid $sp-input-border-active;
            }

            input {
                flex-shrink: 1;
                flex-grow: 1;
                outline: none;
                border: none;
                background-color: transparent;
                padding-left: 50px;

                &:focus {
                    outline: none;
                }

                &::placeholder {
                    @include get-typography('heading-1');
                    line-height: 56px;
                }
            }

            &:after {
                content: attr(data-suffix);
                display: flex;
                width: 34px;
                min-width: 34px;
                height: 34px;
                border-radius: 34px;
                background: $color-base-03;
                color: $color-font-dark;
                align-items: center;
                justify-content: center;
                margin-right: 13px;
                @include get-typography("caption-bold");
            }
        }
    }

    &__buttons { // lp-slider need for overwrite :(
        display: flex; // Updated as flex since Safari breaks things with grid
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 100%;

        .btn {
            min-width: 0;
            overflow: visible;
            position: relative;
            height: 52px;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            flex: 1;
            margin: 0 6px;
            display: flex;
            flex-direction: row;

            &:first-child {
                margin-left: 0;
            }

            &:last-child {
                margin-right: 0;
            }

            svg {
                height: 24px;
                pointer-events: none;
            }

            img {
                pointer-events: none;
                max-height: 48px !important;
                max-width: 75px;
            }

            span {
                margin: 0 8px;
            }

            &[data-method="swish" i] {
                border: 2px solid #00c76b;

                &::before {
                    background: url(/wp-content/themes/phoenix/vectors/icons/circle-check.svg) center/contain no-repeat;
                    content: "";
                    height: 20px;
                    position: absolute;
                    top: -12px;
                    right: 0;
                    width: 20px;
                }
            }
        }

        &--toggle-layout {
            .btn {
                margin: 0;
            }
        }
    }

    &__resume-playing {
        display: inline-block;
        text-align: center;
        @include get-typography("label-bold");
        color: $sp-continue-playing-color;
    }

    &__terms-and-conditions {
        display: inline;
        @include get-typography("caption");
        color: $sp-terms-and-conditions;
        margin-left: 12px;

        a {
            color: $sp-terms-and-conditions;
        }

        @include to(lg) {
            color: $sp-terms-and-conditions-mobile;

            a {
                color: $sp-terms-and-conditions-mobile;
            }
        }

        @include to(lg) {
            display: none;
        }
    }

    &--fixed {
        opacity: 0;
        height: 0;
        overflow: hidden;
        transition: all $transition;

        align-items: center;
        background: $sp-fixed-payment-form-background;
        box-shadow: 0 1px 5px $color-primary-black;

        display: grid;
        grid-auto-flow: column;
        justify-content: center;
        box-sizing: border-box;
        z-index: 9;

        .scrolled-fixed-payment-form & {
            opacity: 1;
            height: auto;
            padding: 24px 18px;
            align-items: center;

            @include to(lg) {
                bottom: 0;
                padding: 12px 12px 16px;
                width: 100%;
            }
        }

        @include to(lg) {
            bottom: auto;
            box-shadow: unset;
            grid-gap: 0px;
        }

        &__terms-and-conditions {
            display: inline;
            color: $color-font-supportive;
            @include get-typography('caption');
            margin: 0;
            margin-left: 12px;

            & a {
                color: $sp-link-color;
            }

            @include to(xxl) {
                max-width: 279px;
            }
        }

        #{$self}__input {
            @include to(lg) {
                height: 56px;
                max-width: 100%;
            }
        }

        #{$self}__buttons--double {
            .btn {
                @include to(lg) {
                    height: 54px;
                    padding: 0;
                    display: flex;
                }
            }
        }
    }

    &--normal {
        background: #ffffff33;
        backdrop-filter: blur(3px);
        border-radius: 20px;
        border: 1px solid #ffffff33;
        box-sizing: border-box;
        margin: 16px auto 32px 0;
        width: 340px;
        padding: 24px;
        max-width: 100%;

        @include to(lg) {
            margin: 16px auto 32px;
            text-align: center;
        }

        .pnp-block-headline__layout--centered & {
            margin: 16px auto 32px;
            text-align: center;
        }

        &-toggle {
            #{$self}__amounts {
                .btn {
                    box-shadow: 0px 4px 16px 0px #FFFFFF66;
                }
            }
        }

        #{$self}__buttons {
            .btn {
                padding: 0 12px;
            }
        }
    }

    &--mobile {
        display: none;

        @include to(lg) {
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
        }

        &-toggle {
            #{$self}__amounts {
                .btn {
                    box-shadow: 0px 4px 16px 0px #FFFFFF66;
                }
            }
        }

        #{$self}__buttons {
            .btn {
                padding: 0 8px;
            }
        }
    }

    &--desktop {
        display: grid;

        @include to(lg) {
            display: none;
        }

        @include from(lg) {
            grid-gap: 16px;
        }

        #{$self}__amounts {
            @include from(lg) {
                display: none;
            }
        }

        #{$self}__input {
            margin-bottom: 0;
        }

        #{$self}__buttons {
            .btn {
                padding: 0 36px;
            }
        }

        &-toggle {
            align-items: flex-start;

            #{$self}__deposit-limits {
                display: none;
            }

            #{$self}__input {
                max-width: 200px;
            }

            #{$self}__amounts {
                margin: 16px 0;
            }

            #{$self}--fixed {
                &__terms-and-conditions {
                    align-self: center;
                    justify-content: flex-start;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 0 5px;
                    margin-right: 100px; // for clashing Chat button
                    margin-left: 0;
                }
            }

            svg {
                padding: 0 20px;
            }
        }
    }

    &__toggle {
        display: block;
        border-radius: 100px;

        &__options {
            border: none;
            display: flex;
            border-radius: 30px;
            justify-content: space-evenly;
            align-items: center;
            height: 36px;
            position: relative;
            margin-bottom: 16px;

            #{$self}--desktop & {
                margin-bottom: 0;
            }

            &:before {
                width: 100%;
                content: '';
                background-color: #000;
                position: absolute;
                border-radius: 57px;
                opacity: 0.20;
                height: 40px;
                top: -1px;
                left: -1px;
            }
        }

        #{$self}--mobile & {
            width: 100%;
        }

        &__selected {
            --selected-option-width: 100%; /* Fallback */
            width: var(--selected-option-width);
            height: 100%;
            position: absolute;
            top: 0;
            background-color: $button-background;
            border-radius: 100px;
            z-index: 0;
            transition: all $transition;
        }

        input[name="payment-method"] {
            display: none;
        }

        &__option {
            width: 100%;
            border-radius: 100px;
            outline: 0;
            text-align: center;
            float: left;
            height: 56px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1;

            & svg {
                margin: 0 auto;
                max-width: 98px;
            }
        }

        /* one item */
        &__option:last-of-type:nth-of-type(1) ~ &__selected {
            --selected-option-width: calc(100% / 1);
        }

        /* two items */
        &__option:last-of-type:nth-of-type(2) ~ &__selected {
            --selected-option-width: calc(100% / 2 - 4px);
        }

        /* three items */
        &__option:last-of-type:nth-of-type(3) ~ &__selected {
            --selected-option-width: calc(100% / 3);
        }

        /* four items */
        &__option:last-of-type:nth-of-type(4) ~ &__selected {
            --selected-option-width: calc(100% / 4);
        }

        /* Position slider based on selected radio */
        input[name="payment-method"]:nth-of-type(1):checked ~ &__selected {
            left: 0;
        }

        input[name="payment-method"]:nth-of-type(2):checked ~ &__selected {
            left: calc(var(--selected-option-width) * 1);
        }

        input[name="payment-method"]:nth-of-type(3):checked ~ &__selected {
            left: calc(var(--selected-option-width) * 2);
        }

        input[name="payment-method"]:nth-of-type(4):checked ~ &__selected {
            left: calc(var(--selected-option-width) * 3);
        }
    }

    &__deposit-limits {
        @include get-typography('subcaption');

        color: $color-font-light;
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 0 6px;

        & span {
            margin: 8px 0 16px 0;

            &:first-child {
                float: left;
            }

            &:last-child {
                float: right;
            }
        }
    }
}