.payment-form {
    $self: &;

    &__amounts {
        display: flex;
        gap: 12px;
        width: 100%;
        margin-bottom: 16px;

        &__button {
            @extend .btn--white;

            &.btn--secondary {
                background: $button-background;
                border-color: $button-background;
                color: $button-color;

                &:hover {
                    background: $button-hover;
                    border-color: $button-hover;
                    color: $button-color;
                }
            }
        }
    }

    &--mobile {
        border-top: 1px solid $color-primary-dark-bluish-grey;

        #{$self}__deposit-limits {
            color: $color-font-dark;
        }
    }

    &--desktop {
        #{$self}__deposit-limits {
            color: $color-font-dark;
        }
    }

    &__input--with-suffix:after {
        background-color: $color-primary-dark-grey;
    }

    &__toggle {
        &__options {
            border-radius: 57px;
            backdrop-filter: blur(20px);
            &:before {
                opacity: 0.3;
            }
        }

        &__option {
            border-radius: 20px;
            height: 38px;

            & svg {
                margin: 0 auto;
                max-width: 68px;
            }
        }

        &__selected {
            background-color: rgba(0, 0, 0, .3);
            border: 1px solid $color-primary-white;
        }
    }
}